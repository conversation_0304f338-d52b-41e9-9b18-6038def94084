# 🔐 Güvenlik Yapılandırması - Environment Security Keys

## 🚨 ÖNEMLİ GÜVENLIK UYARISI

Bu proje artık **her environment için farklı SecurityKey** kullanmaktadır. Bu kritik güvenlik önlemidir.

## 📋 Environment Yapılandırması

### 1. Development (dev)
- **SecurityKey**: Kod içinde tanımlı (sadece development için)
- **Token Süresi**: 60 dakika
- **Kullanım**: Yerel geliştirme ortamı

### 2. Staging (staging)
- **SecurityKey**: Kod içinde tanımlı (test ortamı için)
- **Token Süresi**: 15 dakika
- **Kullanım**: Test ve staging ortamı

### 3. Production (canlı)
- **SecurityKey**: **MUTLAKA Environment Variable kullanın!**
- **Token Süresi**: 15 dakika
- **Kullanım**: Canlı production ortamı

## 🔧 Production Deployment İçin Environment Variables

### Windows Server (IIS)
```cmd
# System Environment Variable olarak ekleyin
setx GYM_SECURITY_KEY_CANLI "YourSuperSecureProductionKey128CharactersLongWithRandomCharacters..." /M
```

### Linux Server (Docker/Systemd)
```bash
# Environment variable olarak ekleyin
export GYM_SECURITY_KEY_CANLI="YourSuperSecureProductionKey128CharactersLongWithRandomCharacters..."

# Veya .env dosyasında
echo "GYM_SECURITY_KEY_CANLI=YourSuperSecureProductionKey128CharactersLongWithRandomCharacters..." >> .env
```

### Docker Compose
```yaml
services:
  gymapi:
    environment:
      - GYM_SECURITY_KEY_CANLI=YourSuperSecureProductionKey128CharactersLongWithRandomCharacters...
```

### Azure App Service
```bash
# Azure CLI ile
az webapp config appsettings set --resource-group myResourceGroup --name myAppName --settings GYM_SECURITY_KEY_CANLI="YourSuperSecureProductionKey128CharactersLongWithRandomCharacters..."
```

## 🔑 Güçlü SecurityKey Oluşturma

### PowerShell ile
```powershell
# 128 karakterlik güçlü key oluşturma
$chars = "ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789_-"
$key = -join ((1..128) | ForEach {$chars[(Get-Random -Maximum $chars.Length)]})
Write-Host "Production SecurityKey: $key"
```

### Linux/Mac ile
```bash
# 128 karakterlik güçlü key oluşturma
openssl rand -base64 96 | tr -d "=+/" | cut -c1-128
```

## ✅ Güvenlik Kontrolleri

Sistem otomatik olarak şu kontrolleri yapar:

1. **Minimum Uzunluk**: SecurityKey en az 64 karakter olmalı
2. **Karakter Kontrolü**: Sadece alfanumerik ve `_-` karakterleri kabul edilir
3. **Environment Kontrolü**: Production'da environment variable kontrolü
4. **Fallback Koruması**: Environment variable yoksa güvenli fallback

## 🚀 Deployment Checklist

### Production'a geçmeden önce:

- [ ] Production SecurityKey environment variable olarak tanımlandı
- [ ] SecurityKey en az 128 karakter uzunluğunda
- [ ] SecurityKey güçlü ve rastgele oluşturuldu
- [ ] Development ve Staging key'leri production'dan farklı
- [ ] Environment variable doğru isimde tanımlandı: `GYM_SECURITY_KEY_CANLI`
- [ ] Uygulama restart edildi
- [ ] Token oluşturma test edildi

## 🔍 Sorun Giderme

### Hata: "SecurityKey güvenlik gereksinimlerini karşılamıyor"
- SecurityKey en az 64 karakter olmalı
- Sadece alfanumerik ve `_-` karakterleri kullanın

### Hata: "TokenOptions bulunamadı"
- appsettings.json'da environment tanımı kontrol edin
- Environment değişkeni doğru set edilmiş mi kontrol edin

### Hata: "Environment variable bulunamadı"
- Production'da `GYM_SECURITY_KEY_CANLI` environment variable'ı tanımlı mı?
- Uygulama restart edildi mi?

## 📞 Destek

Güvenlik ile ilgili sorularınız için sistem yöneticisi ile iletişime geçin.

**NOT**: Bu dosyayı production sunucusuna kopyalamayın. Sadece deployment rehberi olarak kullanın.
