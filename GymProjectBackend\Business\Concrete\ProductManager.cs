﻿﻿using Business.Abstract;
using Business.BusinessAscpects.Autofac;
using Business.Constants;
using Core.Aspects.Autofac.Caching;
using Core.Aspects.Autofac.Logging;
using Core.Aspects.Autofac.Performance;
using Core.Utilities.Paging;
using Core.Utilities.Results;
using Core.Utilities.Security.CompanyContext;
using DataAccess.Abstract;
using DataAccess.Concrete.EntityFramework;
using Entities.Concrete;
using System.Collections.Generic;

namespace Business.Concrete
{
    public class ProductManager : IProductService
    {
        IProductDal _productDal;
        private readonly ICompanyContext _companyContext;

        public ProductManager(IProductDal productDal, ICompanyContext companyContext)
        {
            _productDal = productDal;
            _companyContext = companyContext;
        }
        [SecuredOperation("owner,admin")]
        [LogAspect]
        [PerformanceAspect(3)]
        [SmartCacheRemoveAspect("Product")]
        public IResult Add(Product product)
        {
            _productDal.Add(product);
            return new SuccessResult(Messages.ProductAdded);
        }
        [SecuredOperation("owner,admin")]
        [LogAspect]
        [SmartCacheRemoveAspect("Product")]
        public IResult Delete(int productId)
        {
            // Silinecek Product'ı al - CompanyID kontrolü ile güvenli erişim
            var product = _productDal.Get(p => p.ProductID == productId && p.CompanyID == _companyContext.GetCompanyId());
            if (product == null)
            {
                return new ErrorResult("Ürün bulunamadı veya erişim yetkiniz yok.");
            }

            // Soft delete yap
            product.IsActive = false;
            product.DeletedDate = DateTime.Now;
            _productDal.Update(product);

            return new SuccessResult(Messages.ProductDeleted);
        }
        [SecuredOperation("owner,admin")]
        [MultiTenantCacheAspect(duration: 60, "Product", "Active")]
        public IDataResult<List<Product>> GetAll()
        {
            return new SuccessDataResult<List<Product>>(_productDal.GetAll(m => m.IsActive == true));

        }

        [SecuredOperation("owner,admin")]
        [PerformanceAspect(3)]
        public IDataResult<PaginatedResult<Product>> GetAllPaginated(ProductPagingParameters parameters)
        {
            var result = _productDal.GetAllPaginated(parameters);
            return new SuccessDataResult<PaginatedResult<Product>>(result, Messages.ProductsListed);
        }
        [SecuredOperation("owner,admin")]
        [PerformanceAspect(3)]
        [MultiTenantCacheAspect(duration: 60, "Product", "ById")]
        public IDataResult<Product> GetById(int productId)
        {
            var product = _productDal.Get(p => p.ProductID == productId && p.CompanyID == _companyContext.GetCompanyId());
            if (product == null)
            {
                return new ErrorDataResult<Product>("Ürün bulunamadı veya erişim yetkiniz yok.");
            }
            return new SuccessDataResult<Product>(product);
        }
        [SecuredOperation("owner,admin")]
        [LogAspect]
        [PerformanceAspect(3)]
        [SmartCacheRemoveAspect("Product")]
        public IResult Update(Product product)
        {
            // Mevcut Product'ı al - CompanyID kontrolü ile güvenli erişim
            var existingProduct = _productDal.Get(p => p.ProductID == product.ProductID && p.CompanyID == _companyContext.GetCompanyId());
            if (existingProduct == null)
            {
                return new ErrorResult("Ürün bulunamadı veya erişim yetkiniz yok.");
            }

            // Güvenlik için CompanyID'yi tekrar ata ve CreationDate'i koru
            product.CompanyID = _companyContext.GetCompanyId();
            product.CreationDate = existingProduct.CreationDate;
            product.UpdatedDate = DateTime.Now;

            _productDal.Update(product);
            return new SuccessResult(Messages.ProductUpdated);
        }
    }
}
